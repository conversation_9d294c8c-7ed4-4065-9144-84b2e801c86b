# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/15 14:26 
# @Description  : service_of_answer_detect.py
import json

import cv2
import time
import datetime
import traceback

from correction_service.common import CorrectionContext
from correction_service.data import CorrectRequest, AnswerDetectRequest
from correction_service.data import AnswerDetectResponse, BookResponse, CorrectResponse
from correction_service.util import <PERSON>ic<PERSON>til
from correction_service.service.service_of_ocr import OcrService
from correction_service.service.service_of_answer import AnswerService
from correction_service.service.service_of_internal_answer_detect import IAnswerDetectService

from base_common import Context, OssUtil, LoggerFactory, TopicType as TT, \
    CorrectionError as CE, ProcessError as PE, Constants, ErrorCode as EC, FileUtil, ImageUtil, OkHttpUtil

log = LoggerFactory.get_logger('AnswerDetectService')

class AnswerDetectService:
    def __init__(self):
        self._ans_ser = None
        self._ocr_ser = None
        self._internal_ser = None

    def autowired(self, ans_ser: AnswerService, ocr_ser: OcrService, internal_ser: IAnswerDetectService):
        self._ans_ser = ans_ser
        self._ocr_ser = ocr_ser
        self._internal_ser = internal_ser

    def internal_answer_detect(self, req_data: CorrectRequest, answer_req_data: AnswerDetectRequest, user_img, img_key, img_type):
        if not Context.is_product():
            log.info(f"(mission_id: {req_data.get_mission_id()}) 答案检测请求：{answer_req_data.to_string()}")
        return self._internal_ser.req_answer_detect(req_data, answer_req_data, user_img, img_key, img_type)

    def do_answer_detect(self, req_data: CorrectRequest, book_data: BookResponse) -> AnswerDetectResponse:
        t0 = time.time()
        page_data = book_data.get_page_data()
        answer_data = self._ans_ser.do_collect(req_data, book_data)
        if not answer_data.is_success():
            correct_resp = CorrectResponse(message=str(answer_data.get_message()), error_code=answer_data.get_error_code(),
                            origin_image_url=req_data.get_image_url(), align_info=answer_data.get_align_info(),
                            book_id=req_data.get_book_id(), page_id=page_data.get_page_id())
            return AnswerDetectResponse(st=t0, response=correct_resp)
        t0 = time.time()
        answer_item = answer_data.get_answer_item()
        if not Context.is_product():
            log.info(f"(mission_id: {req_data.get_mission_id()}) 题目区域坐标：{json.dumps(answer_item.get_ref_coords())}")
        if req_data.is_correction():
            book_json = page_data.get_book_json()
            page_id = page_data.get_page_id()
            resp = book_data.get_search_data().get_response()
            search_page = resp.get('pageId')
            pageurl = None
            pagePath = None
            for page in book_json['bookItems']:
                if page['pageId'] == int(page_id):
                    pageurl = page['pageUrl']
                    pagePath = page.get('localPagePath', None)
                    break

            pg_name = str(search_page) + str(time.time())
            # daily保存对齐后图片到当前目录tmp，并进行上传，预发online则保存到nas盘并拼接url
            savepath = f'{Constants.TEMP_PATH}/{pg_name}.jpg'
            save_ret = cv2.imwrite(savepath, answer_item.get_img_align())
            #img_base64 = ImageUtil.numpy_2_base64(answer_item.get_img_align())

            if not save_ret:
                FileUtil.remove_file(savepath)
                Context.report_error(req_data.get_mission_id(), PE.SAVE_ALIGN_IMAGE)
                log.error(f"(mission_id: {req_data.get_mission_id()}) message: {str(CE.UPLOAD_ALIGN_PAGE_FAILED)}")
                correct_resp = CorrectResponse(message=str(CE.UPLOAD_ALIGN_PAGE_FAILED), error_code=EC.CORRECTION_ERROR,
                                               origin_image_url=req_data.get_image_url(), page_id=page_id,
                                               book_id=req_data.get_book_id())
                return AnswerDetectResponse(st=t0, response=correct_resp)

            today = datetime.date.today()
            formatted_today = today.strftime('%Y%m%d')

            alignurl = None
            try:
                # alignurl = OssUtil.upload(savepath, f"piccorrect/{req_data.get_env()}/alignimages_tmp180/{formatted_today}/{pg_name}.jpg")
                alignurl = "https://ajlivebj.oss-cn-beijing.aliyuncs.com/piccorrect/prepub/alignimages_tmp180/20250603/14131141748941911.620162.jpg"
                Context.report_cost(req_data.get_mission_id(), ("上传校正图像", time.time() - t0))
            except:
                log.error(f"(mission_id: {req_data.get_mission_id()}) 上传校正图像失败： {traceback.format_exc()}")
                Context.report_error(req_data.get_mission_id(), PE.SAVE_ALIGN_IMAGE)
                correct_resp = CorrectResponse(message=str(CE.UPLOAD_ALIGN_PAGE_FAILED), error_code=EC.CORRECTION_ERROR,
                                               origin_image_url=req_data.get_image_url(), page_id=page_id,
                                               book_id=req_data.get_book_id())
                return AnswerDetectResponse(st=t0, response=correct_resp)
            finally:
                FileUtil.remove_file(savepath)

            if req_data.get_user_id() is not None:
                save_align_url = Context.get_save_align_url(req_data.get_env())
                try:
                    resp = OkHttpUtil.post_request(save_align_url, str_data={
                        'recordId': req_data.get_record_id(), 'imageUrl': alignurl
                    }, timeout=1)
                    log.debug(f"(mission_id: {req_data.get_mission_id()}) 将校正后图像传给后端成功 响应结果： {json.dumps(resp.get_json_response(), ensure_ascii=False)}")
                except:
                    log.error(f"(mission_id: {req_data.get_mission_id()}) 将校正后图像传给后端失败：\n{traceback.format_exc()}")

            t0 = time.time()
            if not Context.is_product():
                log.info(f'(mission_id: {req_data.get_mission_id()}) 校正后图像url：{alignurl}')
            tmp_crop = page_data.get_col_info()['coord'] if '_' in search_page else []
            #if answer_data.is_all_ai_oral_calc():
            #    answer_item.get_ref_coords()[page_id] = [0, 0, answer_item.get_w() - 1, answer_item.get_h() - 1]
            #    answer_item.get_items_type()[page_id] = TT.INTELLIGENT_ORAL_CALC

            CorrectionContext.get_correct_data(req_data.get_mission_id()).set_align_url(alignurl)
            answer_req_data = AnswerDetectRequest(url_ori=pageurl,
                                                url_photo=alignurl,
                                                items=answer_item.get_ref_coords(),
                                                items_type=answer_item.get_items_type(),
                                                origin_image_url=req_data.get_image_url(),
                                                items_answer=answer_item.get_items_answer(),
                                                boxs=answer_item.get_reg_coords(),
                                                photo_crop=answer_item.get_crop_coord(),
                                                temp_crop=tmp_crop,
                                                verify_cal=answer_item.get_verify_cal_dict(),
                                                pdf_path=pagePath,
                                                surf_mat=answer_data.get_surf_mat())
            # 答案检查
            ad_resp = self.internal_answer_detect(req_data, answer_req_data, answer_item.get_img_align(), answer_item.get_img_key(), answer_item.get_img_type())
            if not ad_resp.is_success():
                Context.report_error(req_data.get_mission_id(), PE.ANSWER_DETECT)
                correct_resp = CorrectResponse(message=str(CE.ACCESS_ANSWER_DETECT_API_FAILED), error_code=EC.CORRECTION_ERROR,
                                               origin_image_url=req_data.get_image_url(), page_id=page_id)
                return AnswerDetectResponse(st=t0, response=correct_resp)
            final_result = ad_resp.to_dict()
            reg_coords = final_result['boxs']
            TopicUtil.adjust_coords(reg_coords)
            self._ocr_ser.do_hand_write_ocr(req_data, answer_item, book_data, final_result, reg_coords)
            if req_data.get_escapeCalcAlgorithmJudge():
                exist_off_compute_flag = False
                for i in answer_item.get_items_type():
                    if TT.OFF_COMPUTE == answer_item.get_items_type()[i]:
                        exist_off_compute_flag = True
                        break
                if exist_off_compute_flag:
                    try:
                        self._internal_ser.tuoshi_correct(answer_item, req_data)
                    except:
                        log.error(f"脱式题批改失败{traceback.format_exc()}")
        return AnswerDetectResponse(st=t0, response=answer_data, success=True)
